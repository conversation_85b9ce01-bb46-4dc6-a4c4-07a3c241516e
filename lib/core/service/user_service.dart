import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/follower/follower_model.dart';
import 'package:toii_social/model/user/update_user_request_model.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'user_service.g.dart';

@RestApi()
abstract class UserService {
  factory UserService(Dio dio, {String baseUrl}) = _UserService;

  @GET('/user/api/v1/users/{user_id}/profile')
  Future<BaseResponse<UserModel>> getUserStats(@Path('user_id') String userId);

  @GET('/user/api/v1/follows/following')
  Future<BaseResponse<FollowingListModel>> getFollowing();

  // Get following list for a user

  // New methods for API response with relationship structure
  @GET('/user/api/v1/follows/followers')
  Future<FollowerApiResponseModel> getFollowersNew();

  @GET('/user/api/v1/follows/following')
  Future<FollowerApiResponseModel> getFollowingNew();

  // Get following list for a user with new structure
  @GET('/user/api/v1/following/{user_id}')
  Future<FollowerApiResponseModel> getUserFollowingNew(
    @Path('user_id') String userId,
  );

  @PUT('/user/api/v1/users/{user_id}')
  Future<BaseResponse<UserModel>> updateUser(
    @Path('user_id') String userId,
    @Body() UpdateUserRequestModel request,
  );
}
